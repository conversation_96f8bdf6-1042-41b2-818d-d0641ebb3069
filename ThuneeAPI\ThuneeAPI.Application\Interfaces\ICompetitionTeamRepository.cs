using ThuneeAPI.Core.Entities;

namespace ThuneeAPI.Application.Interfaces;

/// <summary>
/// Repository interface for CompetitionTeam entity operations
/// </summary>
public interface ICompetitionTeamRepository
{
    /// <summary>
    /// Creates a new competition team
    /// </summary>
    /// <param name="competitionTeam">CompetitionTeam entity to create</param>
    /// <returns>Created competition team with generated ID</returns>
    Task<CompetitionTeam> CreateAsync(CompetitionTeam competitionTeam);

    /// <summary>
    /// Gets a competition team by its ID
    /// </summary>
    /// <param name="id">Team ID</param>
    /// <returns>CompetitionTeam entity or null if not found</returns>
    Task<CompetitionTeam?> GetByIdAsync(Guid id);

    /// <summary>
    /// Gets a competition team by invite code
    /// </summary>
    /// <param name="inviteCode">Invite code</param>
    /// <returns>CompetitionTeam entity or null if not found</returns>
    Task<CompetitionTeam?> GetByInviteCodeAsync(string inviteCode);

    /// <summary>
    /// Gets all teams for a specific competition
    /// </summary>
    /// <param name="competitionId">Competition ID</param>
    /// <returns>Collection of competition teams</returns>
    Task<IEnumerable<CompetitionTeam>> GetByCompetitionIdAsync(Guid competitionId);

    /// <summary>
    /// Gets a team by player ID and competition ID
    /// </summary>
    /// <param name="competitionId">Competition ID</param>
    /// <param name="playerId">Player ID</param>
    /// <returns>CompetitionTeam entity or null if not found</returns>
    Task<CompetitionTeam?> GetByPlayerAndCompetitionAsync(Guid competitionId, Guid playerId);

    /// <summary>
    /// Gets teams that have advanced to the next phase for a specific competition
    /// </summary>
    /// <param name="competitionId">Competition ID</param>
    /// <returns>Collection of teams that have AdvancedToNextPhase = true</returns>
    Task<IEnumerable<CompetitionTeam>> GetAdvancedTeamsByCompetitionAsync(Guid competitionId);

    /// <summary>
    /// Updates a competition team (used for joining partner)
    /// </summary>
    /// <param name="competitionTeam">CompetitionTeam entity to update</param>
    /// <returns>Updated competition team</returns>
    Task<CompetitionTeam> UpdateAsync(CompetitionTeam competitionTeam);

    /// <summary>
    /// Joins a team using invite code
    /// </summary>
    /// <param name="inviteCode">Invite code</param>
    /// <param name="player2Id">Player 2 ID</param>
    /// <returns>Updated competition team</returns>
    Task<CompetitionTeam> JoinTeamAsync(string inviteCode, Guid player2Id);
}
