<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  
  <!-- User Management Stored Procedures -->
  <data name="SP_CreateUser" xml:space="preserve">
    <value>
CREATE PROCEDURE [dbo].[SP_CreateUser]
    @Id UNIQUEIDENTIFIER,
    @Username NVARCHAR(50),
    @Email NVARCHAR(255),
    @PasswordHash NVARCHAR(255),
    @IsVerified BIT = 0,
    @IsActive BIT = 1,
    @IsAdmin BIT = 0
AS
BEGIN
    SET NOCOUNT ON;
    
    INSERT INTO Users (Id, Username, Email, PasswordHash, IsVerified, IsActive, IsAdmin, CreatedAt, UpdatedAt)
    VALUES (@Id, @Username, @Email, @PasswordHash, @IsVerified, @IsActive, @IsAdmin, GETUTCDATE(), GETUTCDATE());
    
    SELECT * FROM Users WHERE Id = @Id;
END
    </value>
  </data>
  
  <data name="SP_GetUserByUsername" xml:space="preserve">
    <value>
CREATE PROCEDURE [dbo].[SP_GetUserByUsername]
    @Username NVARCHAR(50)
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT * FROM Users 
    WHERE Username = @Username AND IsActive = 1;
END
    </value>
  </data>
  
  <data name="SP_GetUserById" xml:space="preserve">
    <value>
CREATE PROCEDURE [dbo].[SP_GetUserById]
    @Id UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT * FROM Users 
    WHERE Id = @Id AND IsActive = 1;
END
    </value>
  </data>
  
  <data name="SP_UpdateUserLastLogin" xml:space="preserve">
    <value>
CREATE PROCEDURE [dbo].[SP_UpdateUserLastLogin]
    @Id UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    
    UPDATE Users 
    SET LastLoginAt = GETUTCDATE(), UpdatedAt = GETUTCDATE()
    WHERE Id = @Id;
END
    </value>
  </data>
  
  <!-- Game Management Stored Procedures -->
  <data name="SP_CreateGame" xml:space="preserve">
    <value>
CREATE PROCEDURE [dbo].[SP_CreateGame]
    @Id UNIQUEIDENTIFIER,
    @LobbyCode NVARCHAR(6),
    @Team1Name NVARCHAR(50),
    @Team2Name NVARCHAR(50),
    @Team1Player1Id UNIQUEIDENTIFIER,
    @Team1Player2Id UNIQUEIDENTIFIER = NULL,
    @Team2Player1Id UNIQUEIDENTIFIER = NULL,
    @Team2Player2Id UNIQUEIDENTIFIER = NULL,
    @CompetitionId UNIQUEIDENTIFIER = NULL
AS
BEGIN
    SET NOCOUNT ON;

    INSERT INTO Games (Id, LobbyCode, Team1Name, Team2Name, Team1Player1Id, Team1Player2Id,
                      Team2Player1Id, Team2Player2Id, CompetitionId, Status, CreatedAt, UpdatedAt)
    VALUES (@Id, @LobbyCode, @Team1Name, @Team2Name, @Team1Player1Id, @Team1Player2Id,
            @Team2Player1Id, @Team2Player2Id, @CompetitionId, 'waiting', GETUTCDATE(), GETUTCDATE());

    SELECT * FROM Games WHERE Id = @Id;
END
    </value>
  </data>
  
  <data name="SP_GetGameByLobbyCode" xml:space="preserve">
    <value>
CREATE PROCEDURE [dbo].[SP_GetGameByLobbyCode]
    @LobbyCode NVARCHAR(6)
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT g.*, 
           u1.Username AS Team1Player1Name,
           u2.Username AS Team1Player2Name,
           u3.Username AS Team2Player1Name,
           u4.Username AS Team2Player2Name
    FROM Games g
    LEFT JOIN Users u1 ON g.Team1Player1Id = u1.Id
    LEFT JOIN Users u2 ON g.Team1Player2Id = u2.Id
    LEFT JOIN Users u3 ON g.Team2Player1Id = u3.Id
    LEFT JOIN Users u4 ON g.Team2Player2Id = u4.Id
    WHERE g.LobbyCode = @LobbyCode;
END
    </value>
  </data>
  
  <data name="SP_JoinGame" xml:space="preserve">
    <value>
CREATE PROCEDURE [dbo].[SP_JoinGame]
    @LobbyCode NVARCHAR(6),
    @PlayerId UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @GameId UNIQUEIDENTIFIER;
    DECLARE @Team1Player1Id UNIQUEIDENTIFIER;
    DECLARE @Team1Player2Id UNIQUEIDENTIFIER;
    DECLARE @Team2Player1Id UNIQUEIDENTIFIER;
    DECLARE @Team2Player2Id UNIQUEIDENTIFIER;

    -- Get the game and current player assignments
    SELECT @GameId = Id,
           @Team1Player1Id = Team1Player1Id,
           @Team1Player2Id = Team1Player2Id,
           @Team2Player1Id = Team2Player1Id,
           @Team2Player2Id = Team2Player2Id
    FROM Games
    WHERE LobbyCode = @LobbyCode AND Status = 'waiting';

    IF @GameId IS NULL
    BEGIN
        RAISERROR('Game not found or not accepting players', 16, 1);
        RETURN;
    END

    -- Check if player is already in the game
    IF @PlayerId IN (@Team1Player1Id, @Team1Player2Id, @Team2Player1Id, @Team2Player2Id)
    BEGIN
        RAISERROR('Player is already in this game', 16, 1);
        RETURN;
    END

    -- Assign player to the first available slot
    IF @Team1Player2Id IS NULL
    BEGIN
        UPDATE Games SET Team1Player2Id = @PlayerId, UpdatedAt = GETUTCDATE()
        WHERE Id = @GameId;
    END
    ELSE IF @Team2Player1Id IS NULL
    BEGIN
        UPDATE Games SET Team2Player1Id = @PlayerId, UpdatedAt = GETUTCDATE()
        WHERE Id = @GameId;
    END
    ELSE IF @Team2Player2Id IS NULL
    BEGIN
        UPDATE Games SET Team2Player2Id = @PlayerId, UpdatedAt = GETUTCDATE()
        WHERE Id = @GameId;
    END
    ELSE
    BEGIN
        RAISERROR('Game is full', 16, 1);
        RETURN;
    END

    -- Return the updated game
    SELECT * FROM Games WHERE Id = @GameId;
END
    </value>
  </data>
  
  <data name="SP_RecordHandResult" xml:space="preserve">
    <value>
CREATE PROCEDURE [dbo].[SP_RecordHandResult]
    @GameId UNIQUEIDENTIFIER,
    @BallNumber INT,
    @HandNumber INT,
    @WinnerPlayerId UNIQUEIDENTIFIER,
    @Points INT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @HandId UNIQUEIDENTIFIER = NEWID();
    
    INSERT INTO GameHands (Id, GameId, BallNumber, HandNumber, WinnerPlayerId, Points, CompletedAt)
    VALUES (@HandId, @GameId, @BallNumber, @HandNumber, @WinnerPlayerId, @Points, GETUTCDATE());
    
    -- Update game scores (simplified logic)
    UPDATE Games 
    SET Team1Score = CASE 
                        WHEN @WinnerPlayerId IN (Team1Player1Id, Team1Player2Id) 
                        THEN Team1Score + @Points 
                        ELSE Team1Score 
                     END,
        Team2Score = CASE 
                        WHEN @WinnerPlayerId IN (Team2Player1Id, Team2Player2Id) 
                        THEN Team2Score + @Points 
                        ELSE Team2Score 
                     END,
        CurrentHand = @HandNumber + 1,
        UpdatedAt = GETUTCDATE()
    WHERE Id = @GameId;
    
    SELECT @HandId AS HandId;
END
    </value>
  </data>
  
  <!-- Competition Management Stored Procedures -->
  <data name="SP_GetCompetitions" xml:space="preserve">
    <value>
CREATE PROCEDURE [dbo].[SP_GetCompetitions]
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT * FROM Competitions 
    ORDER BY CreatedAt DESC;
END
    </value>
  </data>
  
  <data name="SP_CreateCompetition" xml:space="preserve">
    <value>
CREATE PROCEDURE [dbo].[SP_CreateCompetition]
    @Id UNIQUEIDENTIFIER,
    @Name NVARCHAR(100),
    @Description NVARCHAR(500) = NULL,
    @StartDate DATETIME2,
    @EndDate DATETIME2,
    @MaxTeams INT = 32,
    @EntryFee DECIMAL(10,2) = 0,
    @PrizeFirst NVARCHAR(100) = NULL,
    @PrizeSecond NVARCHAR(100) = NULL,
    @PrizeThird NVARCHAR(100) = NULL,
    @TotalPrizePool DECIMAL(10,2) = NULL,
    @IsPublic BIT = 1,
    @AllowSpectators BIT = 1
AS
BEGIN
    SET NOCOUNT ON;
    
    INSERT INTO Competitions (Id, Name, Description, StartDate, EndDate, MaxTeams, EntryFee, 
                             PrizeFirst, PrizeSecond, PrizeThird, TotalPrizePool, IsPublic, 
                             AllowSpectators, Status, CurrentTeams, CreatedAt, UpdatedAt)
    VALUES (@Id, @Name, @Description, @StartDate, @EndDate, @MaxTeams, @EntryFee,
            @PrizeFirst, @PrizeSecond, @PrizeThird, @TotalPrizePool, @IsPublic,
            @AllowSpectators, 'upcoming', 0, GETUTCDATE(), GETUTCDATE());
    
    SELECT * FROM Competitions WHERE Id = @Id;
END
    </value>
  </data>
  
  <!-- Competition Team Stored Procedures -->
  <data name="SP_CreateCompetitionTeam" xml:space="preserve">
    <value>
CREATE PROCEDURE [dbo].[SP_CreateCompetitionTeam]
    @Id UNIQUEIDENTIFIER,
    @CompetitionId UNIQUEIDENTIFIER,
    @TeamName NVARCHAR(50),
    @Player1Id UNIQUEIDENTIFIER,
    @InviteCode NVARCHAR(10),
    @MaxGames INT = 10
AS
BEGIN
    SET NOCOUNT ON;

    BEGIN TRY
        -- Check if competition exists and is active
        IF NOT EXISTS (SELECT 1 FROM Competitions WHERE Id = @CompetitionId AND Status IN ('upcoming', 'active'))
        BEGIN
            RAISERROR('Competition not found or not accepting teams', 16, 1);
            RETURN;
        END

        -- Check if player is already in this competition
        IF EXISTS (SELECT 1 FROM CompetitionTeams WHERE CompetitionId = @CompetitionId AND (Player1Id = @Player1Id OR Player2Id = @Player1Id))
        BEGIN
            RAISERROR('Player is already registered in this competition', 16, 1);
            RETURN;
        END

        INSERT INTO CompetitionTeams (Id, CompetitionId, TeamName, Player1Id, InviteCode, MaxGames, IsActive, IsComplete, RegisteredAt)
        VALUES (@Id, @CompetitionId, @TeamName, @Player1Id, @InviteCode, @MaxGames, 1, 0, GETUTCDATE());

        -- Update competition team count
        UPDATE Competitions
        SET CurrentTeams = CurrentTeams + 1, UpdatedAt = GETUTCDATE()
        WHERE Id = @CompetitionId;

        SELECT * FROM CompetitionTeams WHERE Id = @Id;
    END TRY
    BEGIN CATCH
        THROW;
    END CATCH
END
    </value>
  </data>

  <data name="SP_JoinCompetitionTeam" xml:space="preserve">
    <value>
CREATE PROCEDURE [dbo].[SP_JoinCompetitionTeam]
    @InviteCode NVARCHAR(10),
    @Player2Id UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;

    BEGIN TRY
        DECLARE @TeamId UNIQUEIDENTIFIER;
        DECLARE @CompetitionId UNIQUEIDENTIFIER;

        -- Find the team by invite code
        SELECT @TeamId = Id, @CompetitionId = CompetitionId
        FROM CompetitionTeams
        WHERE InviteCode = @InviteCode AND IsActive = 1 AND Player2Id IS NULL;

        IF @TeamId IS NULL
        BEGIN
            RAISERROR('Invalid invite code or team is already complete', 16, 1);
            RETURN;
        END

        -- Check if player is already in this competition
        IF EXISTS (SELECT 1 FROM CompetitionTeams WHERE CompetitionId = @CompetitionId AND (Player1Id = @Player2Id OR Player2Id = @Player2Id))
        BEGIN
            RAISERROR('Player is already registered in this competition', 16, 1);
            RETURN;
        END

        -- Update the team with Player2
        UPDATE CompetitionTeams
        SET Player2Id = @Player2Id,
            IsComplete = 1,
            CompletedAt = GETUTCDATE()
        WHERE Id = @TeamId;

        SELECT * FROM CompetitionTeams WHERE Id = @TeamId;
    END TRY
    BEGIN CATCH
        THROW;
    END CATCH
END
    </value>
  </data>

  <!-- Leaderboard Stored Procedures -->
  <data name="SP_GetGlobalLeaderboard" xml:space="preserve">
    <value>
CREATE PROCEDURE [dbo].[SP_GetGlobalLeaderboard]
    @PageSize INT = 20,
    @PageNumber INT = 1
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize;

    -- This is a simplified version - in production you'd calculate actual stats
    SELECT
        ROW_NUMBER() OVER (ORDER BY u.CreatedAt) AS Rank,
        u.Id AS PlayerId,
        u.Username AS PlayerName,
        1000 AS Score, -- Mock data
        10 AS GamesPlayed, -- Mock data
        7 AS GamesWon, -- Mock data
        70.0 AS WinRate -- Mock data
    FROM Users u
    WHERE u.IsActive = 1
    ORDER BY Score DESC
    OFFSET @Offset ROWS
    FETCH NEXT @PageSize ROWS ONLY;
END
    </value>
  </data>

  <!-- Competition Phase Management Stored Procedures -->
  <data name="SP_AdvanceCompetitionPhase" xml:space="preserve">
    <value>SP_AdvanceCompetitionPhase</value>
  </data>

  <data name="SP_GetEligibleTeamsForNextPhase" xml:space="preserve">
    <value>SP_GetEligibleTeamsForNextPhase</value>
  </data>

  <data name="SP_GetEligibleTeamsForPhase" xml:space="preserve">
    <value>SP_GetEligibleTeamsForPhase</value>
  </data>

  <data name="SP_GetAdvancedTeamsByCompetition" xml:space="preserve">
    <value>SP_GetAdvancedTeamsByCompetition</value>
  </data>
</root>
